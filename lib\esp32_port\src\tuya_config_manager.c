/**
 * @file tuya_config_manager.c
 * @brief <PERSON>ya configuration manager with NVS storage support
 */

#include "tuya_config_manager.h"
#include "tuya_config.h"
#include "storage_interface.h"
#include "tuya_log.h"
#include "tuya_config.h"
#include "shipping.h"
#include <string.h>
#include <stdlib.h>
#include "esp_partition.h"
#include "esp_ota_ops.h"
#include "esp_heap_caps.h"
#include "esp_system.h"

// NVS键名定义
#define NVS_KEY_PRODUCT_KEY     "prodkey"
#define NVS_KEY_DEVICE_UUID     "uuid"
#define NVS_KEY_DEVICE_AUTHKEY  "authkey"
#define NVS_KEY_SOFTWARE_VER    "version"

// 静态存储配置数据
static tuya_config_data_t g_config_data = {0};
static bool g_config_loaded = false;

static int read_string_from_nvs(const char *storage_name, const char *key, char *buffer, size_t buffer_size, const char *default_value)
{
    size_t data_len = buffer_size - 1; // 留出空间给null终止符
    int ret = local_storage_get(storage_name, key, (uint8_t*)buffer, &data_len);
    
    if (ret == OPRT_OK && data_len > 0) {
        buffer[data_len] = '\0'; // 确保字符串以null结尾
        TY_LOGD("Read from NVS - %s: %s", key, buffer);
        return OPRT_OK;
    } else {
        // 使用默认值
        if (default_value) {
            strncpy(buffer, default_value, buffer_size - 1);
            buffer[buffer_size - 1] = '\0';
            TY_LOGD("Using default - %s: %s", key, buffer);
        } else {
            buffer[0] = '\0';
            TY_LOGW("No value found for %s and no default provided", key);
        }
        return ret;
    }
}

static int write_string_to_nvs(const char *storage_name, const char *key, const char *value)
{
    if (!key || !value) {
        return OPRT_INVALID_PARM;
    }
    
    size_t value_len = strlen(value);
    int ret = local_storage_set(storage_name, key, (const uint8_t*)value, value_len);
    
    if (ret == OPRT_OK) {
        TY_LOGI("Saved to NVS - %s: %s", key, value);
    } else {
        TY_LOGE("Failed to save to NVS - %s: %d", key, ret);
    }
    
    return ret;
}

int tuya_config_load(void)
{
    size_t data_len = 1024;
    char actual_storage_key[16] = {0};
    char data_buffer[data_len]; // 临时缓冲区，足够存储所有配置数据
    const esp_partition_t* running_ota = esp_ota_get_running_partition();

    TY_LOGI("Loading Tuya configuration from NVS...");
    
    // 读取产品密钥
    read_string_from_nvs(TUYA_NAMESPACE, NVS_KEY_PRODUCT_KEY, 
                        g_config_data.product_key, 
                        sizeof(g_config_data.product_key),
                        NULL);
    
    // 读取设备UUID
    read_string_from_nvs(TUYA_NAMESPACE, NVS_KEY_DEVICE_UUID, 
                        g_config_data.device_uuid, 
                        sizeof(g_config_data.device_uuid),
                        NULL);
    
    // 读取设备认证密钥
    read_string_from_nvs(TUYA_NAMESPACE, NVS_KEY_DEVICE_AUTHKEY, 
                        g_config_data.device_authkey, 
                        sizeof(g_config_data.device_authkey),
                        NULL);
    
    // 读取软件版本
    read_string_from_nvs(TUYA_NAMESPACE, NVS_KEY_SOFTWARE_VER, 
                        g_config_data.software_ver, 
                        sizeof(g_config_data.software_ver),
                        SOFTWARE_VER);

    strncpy(actual_storage_key, TUYA_NAMESPACE, sizeof(actual_storage_key) - 1);
    int ret = local_storage_get(TUYA_NAMESPACE, actual_storage_key, (uint8_t*)data_buffer, &data_len);
    g_config_loaded = true;
    
    // 获取内存信息
    size_t free_heap = esp_get_free_heap_size();
    size_t min_free_heap = esp_get_minimum_free_heap_size();
    size_t largest_free_block = heap_caps_get_largest_free_block(MALLOC_CAP_DEFAULT);
    size_t total_heap = heap_caps_get_total_size(MALLOC_CAP_DEFAULT);
    size_t free_internal = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
    size_t free_spiram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);

    TY_LOGI("====== Loaded Configuration ======");
    TY_LOGI("Product Key: %s", g_config_data.product_key);
    TY_LOGI("Device UUID: %s", g_config_data.device_uuid);
    TY_LOGI("Device AuthKey: %s", g_config_data.device_authkey);
    TY_LOGI("Software Ver: %s", g_config_data.software_ver);
    TY_LOGI("Running Partition: %s", running_ota ? running_ota->label : "Unknown");
    TY_LOGI("Shipping Mode: %s", should_enter_shipping_mode() ? "ON" : "OFF");
    TY_LOGI("Activation data: %s", (ret == OPRT_OK) ? data_buffer : "Not Found");
    TY_LOGI("======== Memory Information =======");
    TY_LOGI("Total Heap: %zu bytes (%.1f KB)", total_heap, total_heap / 1024.0f);
    TY_LOGI("Free Heap: %zu bytes (%.1f KB)", free_heap, free_heap / 1024.0f);
    TY_LOGI("Min Free Heap: %zu bytes (%.1f KB)", min_free_heap, min_free_heap / 1024.0f);
    TY_LOGI("Largest Free Block: %zu bytes (%.1f KB)", largest_free_block, largest_free_block / 1024.0f);
    TY_LOGI("Free Internal RAM: %zu bytes (%.1f KB)", free_internal, free_internal / 1024.0f);
    TY_LOGI("Free SPIRAM: %zu bytes (%.1f KB)", free_spiram, free_spiram / 1024.0f);
    TY_LOGI("Heap Usage: %.1f%%", ((total_heap - free_heap) * 100.0f) / total_heap);
    TY_LOGI("==================================");
    
    return OPRT_OK;
}

int tuya_config_save(const tuya_config_data_t *config)
{
    if (!config) {
        return OPRT_INVALID_PARM;
    }
    
    TY_LOGI("Saving Tuya configuration to NVS...");
    
    int ret = OPRT_OK;
    
    // 保存产品密钥
    if (strlen(config->product_key) > 0) {
        ret = write_string_to_nvs(TUYA_NAMESPACE, NVS_KEY_PRODUCT_KEY, config->product_key);
        if (ret != OPRT_OK) return ret;
    }
    
    // 保存设备UUID
    if (strlen(config->device_uuid) > 0) {
        ret = write_string_to_nvs(TUYA_NAMESPACE, NVS_KEY_DEVICE_UUID, config->device_uuid);
        if (ret != OPRT_OK) return ret;
    }
    
    // 保存设备认证密钥
    if (strlen(config->device_authkey) > 0) {
        ret = write_string_to_nvs(TUYA_NAMESPACE, NVS_KEY_DEVICE_AUTHKEY, config->device_authkey);
        if (ret != OPRT_OK) return ret;
    }
#if 0
    // 保存软件版本
    if (strlen(config->software_ver) > 0) {
        ret = write_string_to_nvs(TUYA_NAMESPACE, NVS_KEY_SOFTWARE_VER, config->software_ver);
        if (ret != OPRT_OK) return ret;
    }
#endif
    // 更新内存中的配置
    memcpy(&g_config_data, config, sizeof(tuya_config_data_t));
    g_config_loaded = true;
    
    TY_LOGI("Configuration saved successfully");
    return OPRT_OK;
}

const tuya_config_data_t* tuya_config_get(void)
{
    if (!g_config_loaded) {
        TY_LOGW("Configuration not loaded, loading now...");
        tuya_config_load();
    }
    
    return &g_config_data;
}

int tuya_config_update_prodkey(const char *new_prodkey)
{
    if (!new_prodkey || strlen(new_prodkey) == 0) {
        return OPRT_INVALID_PARM;
    }
    
    TY_LOGI("Updating device PRODKEY: %s", new_prodkey);
    
    strncpy(g_config_data.product_key, new_prodkey, sizeof(g_config_data.product_key) - 1);
    g_config_data.product_key[sizeof(g_config_data.product_key) - 1] = '\0';
    
    return write_string_to_nvs(TUYA_NAMESPACE, NVS_KEY_PRODUCT_KEY, new_prodkey);
}

int tuya_config_update_uuid(const char *new_uuid)
{
    if (!new_uuid || strlen(new_uuid) == 0) {
        return OPRT_INVALID_PARM;
    }
    
    TY_LOGI("Updating device UUID: %s", new_uuid);
    
    strncpy(g_config_data.device_uuid, new_uuid, sizeof(g_config_data.device_uuid) - 1);
    g_config_data.device_uuid[sizeof(g_config_data.device_uuid) - 1] = '\0';
    
    return write_string_to_nvs(TUYA_NAMESPACE, NVS_KEY_DEVICE_UUID, new_uuid);
}

int tuya_config_update_authkey(const char *new_authkey)
{
    if (!new_authkey || strlen(new_authkey) == 0) {
        return OPRT_INVALID_PARM;
    }
    
    TY_LOGI("Updating device AuthKey: %s", new_authkey);
    
    strncpy(g_config_data.device_authkey, new_authkey, sizeof(g_config_data.device_authkey) - 1);
    g_config_data.device_authkey[sizeof(g_config_data.device_authkey) - 1] = '\0';
    
    return write_string_to_nvs(TUYA_NAMESPACE, NVS_KEY_DEVICE_AUTHKEY, new_authkey);
}

int tuya_config_reset_to_defaults(void)
{
    TY_LOGI("Resetting configuration to defaults...");
    
    // 删除NVS中的配置
    local_storage_del(TUYA_NAMESPACE, NVS_KEY_PRODUCT_KEY);
    local_storage_del(TUYA_NAMESPACE, NVS_KEY_DEVICE_UUID);
    local_storage_del(TUYA_NAMESPACE, NVS_KEY_DEVICE_AUTHKEY);
    //local_storage_del(TUYA_NAMESPACE, NVS_KEY_SOFTWARE_VER);
    
    // 重新加载默认配置
    g_config_loaded = false;
    return tuya_config_load();
}
