#include "improv.h"
#include "tuya_log.h"
#include "esp_wifi.h"
#include "esp_err.h"

namespace improv
{
    void ImprovComponent::loadSavedNetworks() {
        saved_networks_.clear();
        
        Preferences preferences;
        preferences.begin("wifi_creds", true);  // true = 只读模式
        
        size_t networks_count = preferences.getUInt("count", 0);
        TY_LOGI("Loading %d saved networks\n", networks_count);
        
        for (size_t i = 0; i < networks_count; i++) {
            String prefix = "net" + String(i);
            String ssid = preferences.getString((prefix + "_ssid").c_str(), "");
            String password = preferences.getString((prefix + "_pass").c_str(), "");
            
            if (ssid.length() > 0) {
                saved_networks_.push_back(WiFiCredential{
                    std::string(ssid.c_str()),
                    std::string(password.c_str())
                });
                TY_LOGI("Loaded credentials for SSID: %s\n", ssid.c_str());
            }
        }
        
        preferences.end();
    }

    void ImprovComponent::saveWiFiCredential(const std::string& ssid, const std::string& password) {
        TY_LOGI("Saving WiFi credentials...");
        
        WiFiCredential new_cred{ssid, password};
        
        // 检查是否已存在相同SSID
        auto it = std::find(saved_networks_.begin(), saved_networks_.end(), new_cred);
        if (it != saved_networks_.end()) {
            TY_LOGI("Updating existing credentials");
            it->password = password;
        } else {
            TY_LOGI("Adding new credentials");
            if (saved_networks_.size() >= MAX_SAVED_NETWORKS) {
                TY_LOGI("Maximum networks reached, removing oldest");
                saved_networks_.erase(saved_networks_.begin());
            }
            saved_networks_.push_back(new_cred);
        }
    
        // 保存到flash存储
        Preferences preferences;
        if (!preferences.begin("wifi_creds", false)) {
            TY_LOGI("Failed to begin preferences");
            return;
        }
        
        // 保存网络数量
        size_t networks_count = saved_networks_.size();
        preferences.putUInt("count", networks_count);
        TY_LOGI("Saving %d networks\n", networks_count);
        
        // 保存每个网络的凭据
        for (size_t i = 0; i < networks_count; i++) {
            String prefix = "net" + String(i);
            preferences.putString((prefix + "_ssid").c_str(), saved_networks_[i].ssid.c_str());
            preferences.putString((prefix + "_pass").c_str(), saved_networks_[i].password.c_str());
            TY_LOGI("Saved network %d: %s\n", i, saved_networks_[i].ssid.c_str());
        }
        
        preferences.end();
        TY_LOGI("WiFi credentials saved successfully");
    }
    
    bool ImprovComponent::connectToSavedNetworks() {
        if (saved_networks_.empty()) {
            TY_LOGD("No saved networks found");
            return false;
        }
#if 0
        WiFi.disconnect(true, false);
        vTaskDelay(pdMS_TO_TICKS(300));
#endif
        WiFi.mode(WIFI_OFF);
        vTaskDelay(pdMS_TO_TICKS(300));
        WiFi.mode(WIFI_STA);
        vTaskDelay(pdMS_TO_TICKS(300));
        WiFi.setTxPower(WIFI_POWER_15dBm);  // 限制最大功率

        // 尝试连接每个保存的网络
        for (const auto& cred : saved_networks_) {
            TY_LOGI("Attempting to connect to %s\n", cred.ssid.c_str());
            WiFi.begin(cred.ssid.c_str(), cred.password.c_str());

            // 等待连接
            int attempts = 0;
            const int MAX_ATTEMPTS = 3;
            while (attempts < MAX_ATTEMPTS) {
                if (WiFi.status() == WL_CONNECTED) {
                    WiFi.setSleep(true); // 启用WiFi的低功耗模式
                    TY_LOGI("Successfully connected to %s\n", cred.ssid.c_str());
                    return true;
                }
                vTaskDelay(pdMS_TO_TICKS(1000));  // 等待1000);
                attempts++;
            }

            vTaskDelay(pdMS_TO_TICKS(1000));  // 等待500);
        }

        return false;
    }
    
    void ImprovComponent::wifiMonitorTask(void* parameter) {
        const TickType_t CHECK_INTERVAL = pdMS_TO_TICKS(5000);  // 5秒检查一次，平衡响应性和功耗
        const TickType_t POWER_ADJUST_INTERVAL = pdMS_TO_TICKS(10000);  // 10秒调整一次功率
        UBaseType_t stackLeft;
        int count = 0;
        uint32_t last_power_check = 0;
        bool was_connected = false;

        TY_LOGI("wifiMonitorTask started with power management\n");

        // 初始化功率管理 初始化RSSI缓冲区
        for (int i = 0; i < 5; i++)
        {
            last_rssi_readings_[i] = -100; // 初始化为很弱的信号
        }
        rssi_buffer_index_ = 0;
        current_tx_power_ = 15; // 最大功率 15dBm (ESP32-C3)
        last_power_adjust_time_ = 0;

        // 设置初始最大功率（未连接时）
        setMaxTxPower();
        TY_LOGI("WIFI TX Power initialized, set to max power");

        while (true) {
            uint32_t current_time = xTaskGetTickCount();
            bool is_connected = (WiFi.status() == WL_CONNECTED);

            if (!is_connected) {
                if (was_connected) {
                    // 刚断开连接，设置最大功率
                    setMaxTxPower();
                    TY_LOGI("WiFi disconnected, set to max power for reconnection");
                }

                TY_LOGD("WiFi disconnected, attempting to reconnect...");
                // 尝试重新连接到保存的网络
                if (instance_->connectToSavedNetworks()) {
                    TY_LOGI("WiFi reconnected successfully");
                    was_connected = true;
                    // 连接成功后等待一段时间再开始功率调整
                    last_power_check = current_time;
                    continue;
                }
                was_connected = false;
            } else {
                // WiFi已连接
                if (!was_connected) {
                    // 刚连接成功
                    TY_LOGI("WiFi connected, starting power optimization");
                    was_connected = true;
                    last_power_check = current_time;
                }

                // 检查是否需要调整功率（每10秒一次）
                if ((current_time - last_power_check) >= POWER_ADJUST_INTERVAL) {
                    int32_t filtered_rssi = getRSSIWithFiltering();
                    if (filtered_rssi != -100) {  // 有效的RSSI值
                        adjustTxPowerBasedOnRSSI(filtered_rssi);
                    }
                    last_power_check = current_time;
                }
            }

            vTaskDelay(CHECK_INTERVAL);

#if 0
            // 每约10秒打印一次堆栈剩余空间
            if (++count >= 3) {
                stackLeft = uxTaskGetStackHighWaterMark(NULL);
                TY_LOGD("[wifiMonitorTask] Stack high water mark: %d", stackLeft);
                count = 0;
            }
#endif
        }
    }

    // 静态成员初始化
    ImprovComponent* ImprovComponent::instance_ = nullptr;

    // WiFi功率管理静态变量初始化
    int32_t ImprovComponent::last_rssi_readings_[5] = {-100, -100, -100, -100, -100};
    uint8_t ImprovComponent::rssi_buffer_index_ = 0;
    int32_t ImprovComponent::current_tx_power_ = 15;
    uint32_t ImprovComponent::last_power_adjust_time_ = 0;

    void ImprovComponent::setup()
    {
        TY_LOGI("Starting ImprovComponent setup...");
        instance_ = this;  // 设置单例指针
        // 首先初始化WiFi

        // 首先加载保存的网络
        loadSavedNetworks();
        TY_LOGI("Loaded %d saved networks\n", saved_networks_.size());
#if 0
        // 尝试连接保存的网络
        if (!saved_networks_.empty()) {
            TY_LOGI("Attempting to connect to saved networks...");
            if (connectToSavedNetworks()) {
                TY_LOGI("Successfully connected to saved network");
            } else {
                TY_LOGI("Failed to connect to any saved network");
            }
        } else {
            TY_LOGD("No saved networks found during setup");
        }
#endif
        // 创建WiFi监控任务
        xTaskCreate(
            wifiMonitorTask,
            "WiFiMonitor",
            WIFI_MONITOR_STACK_SIZE,
            nullptr,
            WIFI_MONITOR_PRIORITY,
            &wifi_monitor_task_handle_
        );
    }

    bool ImprovComponent::wifi_connect(const std::string &ssid, const std::string &password) {
       TY_LOGI("Attempting to connect to WiFi - SSID: %s\n", ssid.c_str());
#if 0
        WiFi.disconnect(true, false);
        vTaskDelay(pdMS_TO_TICKS(300));
#endif
        WiFi.mode(WIFI_OFF);
        vTaskDelay(pdMS_TO_TICKS(300));
        WiFi.mode(WIFI_STA);
        vTaskDelay(pdMS_TO_TICKS(300));
        WiFi.setTxPower(WIFI_POWER_15dBm);  // 限制最大功率

        WiFi.begin(ssid.c_str(), password.c_str());
        TY_LOGI("Connecting to WiFi...");
    
        int attempts = 0;
        const int max_attempts = 5;
        const unsigned long timeout_per_attempt = 5000;
    
        while (attempts < max_attempts) {
            attempts++;
           TY_LOGI("Connection attempt %d of %d\n", attempts, max_attempts);
    
            unsigned long startTime = millis();
            while (WiFi.status() != WL_CONNECTED) {
                if (millis() - startTime > timeout_per_attempt) {
                    TY_LOGI("Attempt %d timed out\n", attempts);
                    break;
                }
                vTaskDelay(pdMS_TO_TICKS(500));
                TY_LOGI("WiFi status: %d\n", WiFi.status());
            }
    
            if (WiFi.status() == WL_CONNECTED) {
                WiFi.setSleep(true); // 启用WiFi的低功耗模式
                TY_LOGI("\nConnected to WiFi!");
                TY_LOGI("IP address: %s\n", WiFi.localIP().toString().c_str());
                // 保存凭据
                saveWiFiCredential(ssid, password);
                return true;
            }
    
            if (attempts < max_attempts) {
                TY_LOGI("\nWIFI Retrying connection...");
                vTaskDelay(pdMS_TO_TICKS(1000));
                WiFi.begin(ssid.c_str(), password.c_str());
            }
        }
    
        TY_LOGI("\nWiFi connection failed after all attempts");
        return false;
    }

    // WiFi功率管理函数实现
    void ImprovComponent::setMaxTxPower() {
        // ESP32-C3最大发射功率为20.5dBm
        // 使用Arduino WiFi库的功率设置方法
        bool result = WiFi.setTxPower(WIFI_POWER_15dBm);  // 最大功率
        if (result) {
            current_tx_power_ = 15;
            TY_LOGI("WiFi TX power set to maximum: %ddBm", current_tx_power_);
        } else {
            TY_LOGE("Failed to set max TX power");
        }
    }

    int32_t ImprovComponent::getRSSIWithFiltering() {
        if (WiFi.status() != WL_CONNECTED) {
            return -100;  // 无效值
        }

        // 多次采样获取更准确的RSSI值
        const int SAMPLE_COUNT = 3;
        const int SAMPLE_DELAY_MS = 250;  // 250ms间隔采样
        int32_t rssi_samples[SAMPLE_COUNT];
        int32_t rssi_sum = 0;

        // 连续采样多次
        for (int i = 0; i < SAMPLE_COUNT; i++) {
            rssi_samples[i] = WiFi.RSSI();
            rssi_sum += rssi_samples[i];

            if (i < SAMPLE_COUNT - 1) {  // 最后一次不需要延迟
                vTaskDelay(pdMS_TO_TICKS(SAMPLE_DELAY_MS));
            }
        }

        // 计算当前采样的平均值
        int32_t current_rssi = rssi_sum / SAMPLE_COUNT;

        // 添加到历史滤波缓冲区
        last_rssi_readings_[rssi_buffer_index_] = current_rssi;
        rssi_buffer_index_ = (rssi_buffer_index_ + 1) % 5;

        // 计算历史移动平均值（去除最大值和最小值）
        int32_t sum = 0;
        int32_t min_val = last_rssi_readings_[0];
        int32_t max_val = last_rssi_readings_[0];

        for (int i = 0; i < 5; i++) {
            sum += last_rssi_readings_[i];
            if (last_rssi_readings_[i] < min_val) min_val = last_rssi_readings_[i];
            if (last_rssi_readings_[i] > max_val) max_val = last_rssi_readings_[i];
        }

        // 去除最大值和最小值后计算平均值
        int32_t filtered_rssi = (sum - min_val - max_val) / 3;

        TY_LOGD("RSSI: samples=[%d,%d,%d], current_avg=%d, filtered=%d",
               rssi_samples[0], rssi_samples[1], rssi_samples[2], current_rssi, filtered_rssi);
        return filtered_rssi;
    }

    void ImprovComponent::adjustTxPowerBasedOnRSSI(int32_t rssi) {
        // RSSI阈值定义（基于实际测试和IEEE 802.11标准）
        const int32_t RSSI_EXCELLENT = -30;  // 优秀信号
        const int32_t RSSI_GOOD = -50;       // 良好信号
        const int32_t RSSI_FAIR = -60;       // 一般信号
        const int32_t RSSI_POOR = -70;       // 较差信号
        const int32_t RSSI_VERY_POOR = -80;  // 很差信号

        // 滞后控制参数（避免频繁调节）
        const int32_t HYSTERESIS = 3;  // 3dB滞后

        // 功率等级定义（基于链路预算和实际测试优化）
        const wifi_power_t POWER_LEVELS[] = {
            WIFI_POWER_2dBm,        // 最低功率：信号很强时节能
            WIFI_POWER_5dBm,        // 低功率：信号强时
            WIFI_POWER_8_5dBm,      // 中等功率：信号一般时
            WIFI_POWER_11dBm,       // 较高功率：信号较弱时
            WIFI_POWER_13dBm,       // 高功率：信号弱时
            WIFI_POWER_15dBm      // 最大功率：信号很弱或重连时
        };

        const char* POWER_NAMES[] = {
            "2dBm", "5dBm", "8.5dBm", "11dBm", "13dBm", "15dBm"
        };

        const int8_t POWER_VALUES[] = {2, 5, 9, 11, 13, 15};  // 用于比较的数值

        int target_index = 5;  // 默认最大功率

        // 根据RSSI确定目标功率等级（基于链路预算优化）
        if (rssi >= RSSI_EXCELLENT + HYSTERESIS) {
            target_index = 0;  // 2dBm：信号很强，最低功率节能
        } else if (rssi >= RSSI_GOOD + HYSTERESIS) {
            target_index = 1;  // 5dBm：信号强，低功率
        } else if (rssi >= RSSI_FAIR + HYSTERESIS) {
            target_index = 2;  // 8.5dBm：信号一般，中等功率
        } else if (rssi >= RSSI_POOR + HYSTERESIS) {
            target_index = 3;  // 11dBm：信号较弱，需要更多功率
        } else if (rssi >= RSSI_VERY_POOR + HYSTERESIS) {
            target_index = 4;  // 13dBm：信号弱，高功率保证稳定
        } else {
            target_index = 5;  // 15dBm：信号很弱，最大功率
        }

        int8_t target_power_value = POWER_VALUES[target_index];

        // 滞后控制：只有当功率变化超过一个等级时才调整
        if (abs(target_power_value - current_tx_power_) >= 3) {
            // 设置新的发射功率
            bool result = WiFi.setTxPower(POWER_LEVELS[target_index]);

            if (result) {
                TY_LOGI("WiFi TX power adjusted: %s (RSSI: %ddBm)",
                       POWER_NAMES[target_index], rssi);
                current_tx_power_ = target_power_value;
            } else {
                TY_LOGE("Failed to adjust TX power to %s", POWER_NAMES[target_index]);
            }
        } else {
            TY_LOGD("TX power unchanged: %ddBm (RSSI: %ddBm, hysteresis)",
                   current_tx_power_, rssi);
        }
    }

} // namespace improv
