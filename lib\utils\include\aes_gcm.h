/**
 * @file aes_gcm.h
 * @brief AES-GCM encryption/decryption header for TuyaOpen protocol 2.3
 * 
 * This file defines the AES-GCM cipher wrapper functions and data structures
 * needed for TuyaOpen protocol 2.3 compatibility.
 */

#ifndef AES_GCM_H
#define AES_GCM_H

#include <stdint.h>
#include <stddef.h>
#include "tuya_error_code.h"
#include "mbedtls/cipher.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Cipher types supported by the wrapper (using mbedtls definitions)
 */
typedef mbedtls_cipher_type_t cipher_type_t;

/**
 * @brief Cipher parameters structure for AES-GCM operations
 */
typedef struct {
    cipher_type_t cipher_type;    /**< Cipher type (must be MBEDTLS_CIPHER_AES_128_GCM) */
    const uint8_t *key;          /**< Encryption/decryption key */
    size_t key_len;              /**< Key length in bytes (must be 16 for AES-128) */
    const uint8_t *nonce;        /**< Nonce/IV for GCM mode */
    size_t nonce_len;            /**< Nonce length in bytes (typically 12) */
    const uint8_t *ad;           /**< Additional authenticated data */
    size_t ad_len;               /**< Additional data length in bytes */
    const uint8_t *data;         /**< Input data (plaintext for encrypt, ciphertext for decrypt) */
    size_t data_len;             /**< Input data length in bytes */
} cipher_params_t;

/**
 * @brief AES-GCM authenticated encryption wrapper
 * 
 * This function encrypts the input data using AES-128-GCM and generates
 * an authentication tag for integrity verification.
 * 
 * @param params Cipher parameters including key, nonce, additional data, and plaintext
 * @param output Buffer to store encrypted data (must be at least params->data_len bytes)
 * @param olen Pointer to store output length (will be set to params->data_len)
 * @param tag Buffer to store authentication tag (must be at least tag_len bytes)
 * @param tag_len Length of authentication tag (typically 16 bytes)
 * @return OPERATE_RET OPRT_OK on success, error code on failure
 */
OPERATE_RET mbedtls_cipher_auth_encrypt_wrapper(const cipher_params_t *params,
                                                uint8_t *output, size_t *olen,
                                                uint8_t *tag, size_t tag_len);

/**
 * @brief AES-GCM authenticated decryption wrapper
 * 
 * This function decrypts the input data using AES-128-GCM and verifies
 * the authentication tag for integrity checking.
 * 
 * @param params Cipher parameters including key, nonce, additional data, and ciphertext
 * @param output Buffer to store decrypted data (must be at least params->data_len bytes)
 * @param olen Pointer to store output length (will be set to params->data_len)
 * @param tag Authentication tag to verify (must be tag_len bytes)
 * @param tag_len Length of authentication tag (typically 16 bytes)
 * @return OPERATE_RET OPRT_OK on success, OPRT_COM_ERROR on failure (including auth failure)
 */
OPERATE_RET mbedtls_cipher_auth_decrypt_wrapper(const cipher_params_t *params,
                                                uint8_t *output, size_t *olen,
                                                const uint8_t *tag, size_t tag_len);

#ifdef __cplusplus
}
#endif

#endif /* AES_GCM_H */
