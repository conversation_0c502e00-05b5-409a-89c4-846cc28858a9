#ifdef __cplusplus
extern "C" {
#endif

#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include <string.h>

#include "system_interface.h"

/*
 * Time conversion constants.
 */
#define NANOSECONDS_PER_MILLISECOND    ( 1000000L )    /**< @brief Nanoseconds per millisecond. */
#define MILLISECONDS_PER_SECOND        ( 1000L )       /**< @brief Milliseconds per second. */

// 静态内存池配置
#define STATIC_MEMORY_POOL_SIZE     (8192)  // 8KB 内存池
#define MAX_ALLOCATIONS             (32)    // 最大分配块数

// 内存块结构
typedef struct {
    void* ptr;
    size_t size;
    bool in_use;
} memory_block_t;

// 静态内存池
static uint8_t static_memory_pool[STATIC_MEMORY_POOL_SIZE];
static memory_block_t memory_blocks[MAX_ALLOCATIONS];
static size_t pool_offset = 0;
static bool pool_initialized = false;

// 初始化内存池
static void init_memory_pool(void)
{
    if (!pool_initialized) {
        memset(memory_blocks, 0, sizeof(memory_blocks));
        pool_offset = 0;
        pool_initialized = true;
    }
}

// 对齐到4字节边界
static size_t align_size(size_t size)
{
    return (size + 3) & ~3;
}

void* system_malloc(size_t n)
{
    init_memory_pool();
    
    if (n == 0) return NULL;
    
    size_t aligned_size = align_size(n);
    
    // 检查是否有足够的空间
    if (pool_offset + aligned_size > STATIC_MEMORY_POOL_SIZE) {
        // 内存池空间不足，回退到动态分配（但记录警告）
        printf("Warning: Static memory pool exhausted, falling back to malloc\n");
        return malloc(n);
    }
    
    // 找到空闲的内存块记录
    for (int i = 0; i < MAX_ALLOCATIONS; i++) {
        if (!memory_blocks[i].in_use) {
            memory_blocks[i].ptr = &static_memory_pool[pool_offset];
            memory_blocks[i].size = aligned_size;
            memory_blocks[i].in_use = true;
            
            pool_offset += aligned_size;
            return memory_blocks[i].ptr;
        }
    }
    
    // 没有空闲的记录块，回退到动态分配
    printf("Warning: No free memory block records, falling back to malloc\n");
    return malloc(n);
}

void* system_calloc(size_t n, size_t size)
{
    size_t total_size = n * size;
    void* ptr = system_malloc(total_size);
    if (ptr) {
        memset(ptr, 0, total_size);
    }
    return ptr;
}

void system_free(void *ptr)
{
    if (!ptr) return;
    
    init_memory_pool();
    
    // 检查是否是从静态内存池分配的
    bool found = false;
    for (int i = 0; i < MAX_ALLOCATIONS; i++) {
        if (memory_blocks[i].in_use && memory_blocks[i].ptr == ptr) {
            memory_blocks[i].in_use = false;
            found = true;
            break;
        }
    }
    
    // 如果不是从静态内存池分配的，使用标准free
    if (!found) {
        free(ptr);
    }
    
    // 注意：静态内存池不支持内存碎片整理，这是一个简化的实现
    // 在实际应用中，可能需要更复杂的内存管理策略
}

uint32_t system_ticks( void )
{
    TickType_t ticks = xTaskGetTickCount() * portTICK_PERIOD_MS;
    return ( uint32_t ) ticks;
}

uint32_t system_timestamp()
{
    time_t now;
    // struct tm timeinfo;
    time(&now);
    // localtime_r(&now, &timeinfo);
    return (uint32_t)now;
}

uint32_t system_random(void)
{
    return (uint32_t)(0xffffffff & rand());
}

#ifdef __cplusplus
}
#endif
