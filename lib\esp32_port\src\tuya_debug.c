/**
 * @file tuya_debug.c
 * @brief Tuya IoT debugging utilities
 */

#include "tuya_debug.h"
#include "tuya_iot.h"
#include "tuya_config.h"
#include "storage_interface.h"
#include "tuya_log.h"
#include <string.h>

void tuya_debug_analyze_storage_keys(const tuya_iot_config_t *config)
{
    TY_LOGI("=== Storage Key Analysis ===");
    
    if (!config || !config->storage_namespace) {
        TY_LOGE("Invalid config or missing storage_namespace");
        return;
    }
    
    const char *storage_namespace = config->storage_namespace;
    size_t namespace_len = strlen(storage_namespace);
    
    // 主激活数据键
    char activate_key[16] = {0};
    strncpy(activate_key, storage_namespace, sizeof(activate_key) - 1);
    TY_LOGI("Activate data key: %s", activate_key);
    
    // 设备ID键
    char devid_key[32];
    snprintf(devid_key, sizeof(devid_key), "%s.devid", storage_namespace);
    TY_LOGI("Device ID key: %s", devid_key);
    
    // 版本键
    char version_key[32];
    snprintf(version_key, sizeof(version_key), "%s.ver", storage_namespace);
    TY_LOGI("Version key: %s", version_key);
    
    TY_LOGI("============================");
}


