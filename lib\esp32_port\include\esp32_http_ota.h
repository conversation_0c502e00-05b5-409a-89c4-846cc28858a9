#pragma once

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>
#include "tuya_ota.h"

/**
 * @brief 直接通过HTTP下载OTA固件
 *
 * @param url 固件下载URL（HTTP或HTTPS）
 * @param file_size 固件文件大小
 * @param md5 期望的MD5校验值
 * @param tuya_handle Tuya OTA句柄，用于进度和状态报告
 * @return true 下载并升级成功
 * @return false 下载或升级失败
 */
bool direct_http_ota_download(const char* url, size_t file_size, const char* md5, tuya_ota_handle_t* tuya_handle);

#ifdef __cplusplus
}
#endif
