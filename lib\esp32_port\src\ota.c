#include "esp_err.h"
#include "tuya_ota.h"
#include "tuya_log.h"

tuya_ota_handle_t ota_handle;
extern tuya_iot_client_t client;

void user_ota_event_cb(tuya_ota_handle_t *handle, tuya_ota_event_t *event)
{
    esp_err_t ret = ESP_OK;
    switch (event->id)
    {
    case TUYA_OTA_EVENT_START:
        TY_LOGI("OTA start");
        if (ret != ESP_OK)
        {
            TY_LOGE("OTA init error: %d", ret);
            tuya_ota_upgrade_status_report(handle, TUS_DOWNLOAD_ERROR_UNKONW);
        }
        break;

    case TUYA_OTA_EVENT_ON_DATA:
        TY_LOGI("OTA data len: %d %d/%d", event->data_len, event->offset, event->file_size);
        if (ret != ESP_OK)
        {
            TY_LOGE("OTA write error: %d", ret);
            tuya_ota_upgrade_status_report(handle, TUS_DOWNLOAD_ERROR_UNKONW);
        }
        break;

    case TUYA_OTA_EVENT_FINISH:
        TY_LOGI("OTA finish");
        if (ret != ESP_OK)
        {
            TY_LOGE("OTA end error: %d", ret);
            tuya_ota_upgrade_status_report(handle, TUS_DOWNLOAD_ERROR_UNKONW);
        }
        break;
    case TUYA_OTA_EVENT_FAULT:
        TY_LOGE("OTA fault");
        break;
    }
}

