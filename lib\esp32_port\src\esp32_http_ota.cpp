#include <Arduino.h>
#include <string.h>
#include "esp32_http_ota.h"
#include "tuya_log.h"
#include "esp_http_client.h"
#include "esp_ota_ops.h"
#include "esp_app_format.h"
#include "esp_partition.h"
#include "mbedtls/md5.h"
#include "tuya_iot.h"
#include "tuya_ota.h"

#define HTTP_OTA_BUFFER_SIZE (8192)  // 8KB缓冲区，比MQTT方式更大
#define HTTP_OTA_TIMEOUT_MS (30000)  // 30秒超时

typedef struct {
    esp_ota_handle_t ota_handle;
    const esp_partition_t* ota_partition;
    size_t total_size;
    size_t downloaded_size;
    mbedtls_md5_context md5_ctx;
    char expected_md5[33];
    bool ota_started;
    uint32_t start_time;
    tuya_ota_handle_t* tuya_handle;  // Tuya OTA句柄用于进度报告
    int last_reported_percent;       // 上次报告的进度百分比
} http_ota_context_t;

static http_ota_context_t ota_ctx = {0};

// Tuya进度报告函数
static void report_tuya_progress(int percent)
{
    if (ota_ctx.tuya_handle && percent != ota_ctx.last_reported_percent) {
        // 每10%报告一次进度，避免过于频繁
        if (percent % 10 == 0 || percent == 100) {
            tuya_ota_upgrade_progress_report(ota_ctx.tuya_handle, percent);
            TY_LOGI("Tuya OTA progress reported: %d%%", percent);
            ota_ctx.last_reported_percent = percent;
        }
    }
}

// HTTP事件处理函数
static esp_err_t http_event_handler(esp_http_client_event_t *evt)
{
    switch (evt->event_id) {
    case HTTP_EVENT_ERROR:
        TY_LOGE("HTTP_EVENT_ERROR");
        break;
    case HTTP_EVENT_ON_CONNECTED:
        TY_LOGI("HTTP_EVENT_ON_CONNECTED");
        break;
    case HTTP_EVENT_HEADER_SENT:
        TY_LOGD("HTTP_EVENT_HEADER_SENT");
        break;
    case HTTP_EVENT_ON_HEADER:
        TY_LOGD("HTTP_EVENT_ON_HEADER, key=%s, value=%s", evt->header_key, evt->header_value);
        break;
    case HTTP_EVENT_ON_DATA:
        {
            // 处理接收到的数据
            if (!ota_ctx.ota_started) {
                // 第一次接收数据，开始OTA
                ota_ctx.ota_partition = esp_ota_get_next_update_partition(NULL);
                if (ota_ctx.ota_partition == NULL) {
                    TY_LOGE("Failed to get OTA partition");
                    return ESP_FAIL;
                }

                esp_err_t err = esp_ota_begin(ota_ctx.ota_partition, ota_ctx.total_size, &ota_ctx.ota_handle);
                if (err != ESP_OK) {
                    TY_LOGE("esp_ota_begin failed: %s", esp_err_to_name(err));
                    return ESP_FAIL;
                }

                mbedtls_md5_init(&ota_ctx.md5_ctx);
                mbedtls_md5_starts_ret(&ota_ctx.md5_ctx);
                ota_ctx.ota_started = true;
                ota_ctx.start_time = millis();
                TY_LOGI("OTA started, partition: %s", ota_ctx.ota_partition->label);
            }

            // 写入OTA数据
            esp_err_t err = esp_ota_write(ota_ctx.ota_handle, evt->data, evt->data_len);
            if (err != ESP_OK) {
                TY_LOGE("esp_ota_write failed: %s", esp_err_to_name(err));
                return ESP_FAIL;
            }

            // 更新MD5
            mbedtls_md5_update_ret(&ota_ctx.md5_ctx, (const unsigned char*)evt->data, evt->data_len);

            // 更新进度
            ota_ctx.downloaded_size += evt->data_len;

            // 计算进度百分比
            int current_percent = (ota_ctx.downloaded_size * 100) / ota_ctx.total_size;

            // 报告Tuya进度
            report_tuya_progress(current_percent);

            // 每10%进度打印一次详细日志
            static int last_log_percent = -1;
            if (current_percent != last_log_percent && current_percent % 10 == 0) {
                uint32_t elapsed = millis() - ota_ctx.start_time;
                uint32_t speed = (ota_ctx.downloaded_size * 1000) / (elapsed + 1); // bytes/sec
                TY_LOGI("Direct HTTP OTA Progress: %d%% (%zu/%zu bytes, %u B/s)",
                       current_percent, ota_ctx.downloaded_size, ota_ctx.total_size, speed);
                last_log_percent = current_percent;
            }
            break;
        }
    case HTTP_EVENT_ON_FINISH:
        TY_LOGI("HTTP_EVENT_ON_FINISH");
        break;
    case HTTP_EVENT_DISCONNECTED:
        TY_LOGI("HTTP_EVENT_DISCONNECTED");
        break;
    }
    return ESP_OK;
}

// 验证MD5
static bool verify_md5(const char* expected_md5)
{
    unsigned char md5_hash[16];
    char calculated_md5[33];
    
    mbedtls_md5_finish_ret(&ota_ctx.md5_ctx, md5_hash);
    mbedtls_md5_free(&ota_ctx.md5_ctx);
    
    // 转换为十六进制字符串
    for (int i = 0; i < 16; i++) {
        sprintf(calculated_md5 + i * 2, "%02x", md5_hash[i]);
    }
    calculated_md5[32] = '\0';
    
    TY_LOGI("Expected MD5: %s", expected_md5);
    TY_LOGI("Calculated MD5: %s", calculated_md5);
    
    return (strcasecmp(expected_md5, calculated_md5) == 0);
}

bool direct_http_ota_download(const char* url, size_t file_size, const char* md5, tuya_ota_handle_t* tuya_handle)
{
    if (!url || !md5 || file_size == 0) {
        TY_LOGE("Invalid parameters");
        return false;
    }

    // 检查分区大小
    const esp_partition_t* next_partition = esp_ota_get_next_update_partition(NULL);
    if (!next_partition) {
        TY_LOGE("No OTA partition available");
        return false;
    }

    if (file_size > next_partition->size) {
        TY_LOGE("File size (%zu) exceeds OTA partition size (%zu)", file_size, next_partition->size);
        return false;
    }

    TY_LOGI("OTA partition: %s, size: %zu bytes, file size: %zu bytes",
           next_partition->label, next_partition->size, file_size);

    // 初始化上下文
    memset(&ota_ctx, 0, sizeof(ota_ctx));
    ota_ctx.total_size = file_size;
    ota_ctx.tuya_handle = tuya_handle;
    ota_ctx.last_reported_percent = -1;
    strncpy(ota_ctx.expected_md5, md5, sizeof(ota_ctx.expected_md5) - 1);
    
    TY_LOGI("Starting direct HTTP OTA download");
    TY_LOGI("URL: %s", url);
    TY_LOGI("Size: %zu bytes", file_size);
    TY_LOGI("Expected MD5: %s", md5);

    // 报告Tuya开始下载
    if (tuya_handle) {
        tuya_ota_upgrade_status_report(tuya_handle, TUS_UPGRDING);
        TY_LOGI("Tuya OTA status reported: TUS_UPGRDING");
    }
    
    // 配置HTTP客户端
    esp_http_client_config_t config = {};
    config.url = url;
    config.event_handler = http_event_handler;
    config.buffer_size = HTTP_OTA_BUFFER_SIZE;
    config.buffer_size_tx = 1024;
    config.timeout_ms = HTTP_OTA_TIMEOUT_MS;
    
    esp_http_client_handle_t client = esp_http_client_init(&config);
    if (client == NULL) {
        TY_LOGE("Failed to initialize HTTP client");
        return false;
    }
    
    // 执行HTTP GET请求
    esp_err_t err = esp_http_client_perform(client);
    
    bool success = false;
    if (err == ESP_OK) {
        int status_code = esp_http_client_get_status_code(client);
        int content_length = esp_http_client_get_content_length(client);
        
        TY_LOGI("HTTP Status: %d, Content-Length: %d", status_code, content_length);
        
        if (status_code == 200 && ota_ctx.downloaded_size == ota_ctx.total_size) {
            // 验证MD5
            if (verify_md5(ota_ctx.expected_md5)) {
                // 完成OTA
                err = esp_ota_end(ota_ctx.ota_handle);
                if (err == ESP_OK) {
                    err = esp_ota_set_boot_partition(ota_ctx.ota_partition);
                    if (err == ESP_OK) {
                        uint32_t total_time = millis() - ota_ctx.start_time;
                        uint32_t avg_speed = (ota_ctx.downloaded_size * 1000) / (total_time + 1);
                        TY_LOGI("Direct HTTP OTA completed successfully! Time: %u ms, Avg speed: %u B/s",
                               total_time, avg_speed);

                        // 报告100%进度和完成状态
                        report_tuya_progress(100);
                        if (tuya_handle) {
                            tuya_ota_upgrade_status_report(tuya_handle, TUS_UPGRD_FINI);
                            TY_LOGI("Tuya OTA status reported: TUS_UPGRD_FINI");
                        }
                        success = true;
                    } else {
                        TY_LOGE("esp_ota_set_boot_partition failed: %s", esp_err_to_name(err));
                    }
                } else {
                    TY_LOGE("esp_ota_end failed: %s", esp_err_to_name(err));
                }
            } else {
                TY_LOGE("MD5 verification failed");
                esp_ota_abort(ota_ctx.ota_handle);
                if (tuya_handle) {
                    tuya_ota_upgrade_status_report(tuya_handle, TUS_UPGRADE_ERROR_HMAC);
                    TY_LOGI("Tuya OTA status reported: TUS_UPGRADE_ERROR_HMAC (MD5 mismatch)");
                }
            }
        } else {
            TY_LOGE("Download incomplete or HTTP error. Status: %d, Downloaded: %zu/%zu",
                   status_code, ota_ctx.downloaded_size, ota_ctx.total_size);
            if (ota_ctx.ota_started) {
                esp_ota_abort(ota_ctx.ota_handle);
            }
            if (tuya_handle) {
                tuya_ota_upgrade_status_report(tuya_handle, TUS_DOWNLOAD_ERROR_MALLOC_FAIL);
                TY_LOGI("Tuya OTA status reported: TUS_DOWNLOAD_ERROR_MALLOC_FAIL (download incomplete)");
            }
        }
    } else {
        TY_LOGE("HTTP request failed: %s", esp_err_to_name(err));
        if (ota_ctx.ota_started) {
            esp_ota_abort(ota_ctx.ota_handle);
        }
        if (tuya_handle) {
            tuya_ota_upgrade_status_report(tuya_handle, TUS_DOWNLOAD_ERROR_TIMEOUT);
            TY_LOGI("Tuya OTA status reported: TUS_DOWNLOAD_ERROR_TIMEOUT (HTTP request failed)");
        }
    }
    
    esp_http_client_cleanup(client);
    return success;
}
