#ifndef __SYSTEM_INTERFACE_H_
#define __SYSTEM_INTERFACE_H_

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stddef.h>

void* system_malloc(size_t n);

void* system_calloc(size_t n, size_t size);

void  system_free(void *ptr);

uint32_t system_ticks(void);

uint32_t system_timestamp(void);

uint32_t system_random(void);

// 静态内存管理接口
typedef struct {
    void* buffer;
    size_t size;
    uint8_t in_use;
} static_buffer_t;

// 静态缓冲区池管理
void* static_buffer_alloc(size_t size);
void static_buffer_free(void* ptr);
void static_buffer_init(void);

// 预定义的静态缓冲区大小
#define STATIC_BUFFER_SMALL_SIZE    256     // 小缓冲区
#define STATIC_BUFFER_MEDIUM_SIZE   1024    // 中等缓冲区
#define STATIC_BUFFER_LARGE_SIZE    2048    // 大缓冲区
#define STATIC_BUFFER_XLARGE_SIZE   4096    // 超大缓冲区

// 静态缓冲区池数量
#define STATIC_BUFFER_SMALL_COUNT   8
#define STATIC_BUFFER_MEDIUM_COUNT  4
#define STATIC_BUFFER_LARGE_COUNT   2
#define STATIC_BUFFER_XLARGE_COUNT  1

#ifdef __cplusplus
}
#endif

#endif //__TIMER_INTERFACE_H_
