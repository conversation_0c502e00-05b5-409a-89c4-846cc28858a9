#ifndef __DEVICE_CONTROL_H__
#define __DEVICE_CONTROL_H__

#include <Arduino.h>

#ifdef __cplusplus
extern "C" {
#endif
void device_control_init();
void device_control_loop();
void device_control_tuya_iot_status(bool connected);
void device_control_wifi_connected_flash(bool connected);
void handle_button6_click();
void handle_button7_click();
bool is_pump_ozone_running();
#ifdef __cplusplus
}
#endif
#endif // __DEVICE_CONTROL_H__
