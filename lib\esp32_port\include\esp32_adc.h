#pragma once
#include <Arduino.h>
#include "esp_adc_cal.h"
#include "driver/adc.h"

typedef struct
{
    float voltage_divider_factor; // 电压分压系数
    // 单次采样
    uint8_t adc_pin;
    adc_unit_t adc_unit;
    adc1_channel_t adc_channel;
    adc_atten_t adc_atten;       // 0mV ~ 2500mV/0 mV ~ 3100 mV 衰减范围
    adc_bits_width_t adc_width;  // 12位分辨率
    const uint16_t default_vref; // 默认参考电压，单位mV
    // 连续采样
    uint16_t adc1_chan_mask;
    uint16_t adc2_chan_mask; // example for ADC2, BIT(0)
    adc_channel_t channel[1];

    esp_adc_cal_characteristics_t adc_chars;
    esp_adc_cal_value_t cal_type;
} adc_config_t;

#ifdef __cplusplus
extern "C" {
#endif
void adc_calibration(adc_config_t *adc_config);
void adc_single_init(adc_config_t *adc_config);
void adc_continuous_init(adc_config_t *adc_config);
float ReadVoltageContinuous(adc_config_t *adc_config);
float ReadVoltageSingle(adc_config_t *adc_config);
#ifdef __cplusplus
}
#endif