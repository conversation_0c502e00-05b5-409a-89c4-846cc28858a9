#ifdef __cplusplus
extern "C" {
#endif

#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"

#include "system_interface.h"

/*
 * Time conversion constants.
 */
#define NANOSECONDS_PER_MILLISECOND    ( 1000000L )    /**< @brief Nanoseconds per millisecond. */
#define MILLISECONDS_PER_SECOND        ( 1000L )       /**< @brief Milliseconds per second. */

// 静态内存池定义
static uint8_t small_buffers[STATIC_BUFFER_SMALL_COUNT][STATIC_BUFFER_SMALL_SIZE];
static uint8_t medium_buffers[STATIC_BUFFER_MEDIUM_COUNT][STATIC_BUFFER_MEDIUM_SIZE];
static uint8_t large_buffers[STATIC_BUFFER_LARGE_COUNT][STATIC_BUFFER_LARGE_SIZE];
static uint8_t xlarge_buffers[STATIC_BUFFER_XLARGE_COUNT][STATIC_BUFFER_XLARGE_SIZE];

static static_buffer_t small_pool[STATIC_BUFFER_SMALL_COUNT];
static static_buffer_t medium_pool[STATIC_BUFFER_MEDIUM_COUNT];
static static_buffer_t large_pool[STATIC_BUFFER_LARGE_COUNT];
static static_buffer_t xlarge_pool[STATIC_BUFFER_XLARGE_COUNT];

static SemaphoreHandle_t buffer_mutex = NULL;
static bool static_buffer_initialized = false;

void* system_malloc(size_t n)
{
    return malloc(n);
}

void* system_calloc(size_t n, size_t size)
{
    return calloc(n, size);
}

void  system_free(void *ptr)
{
    free(ptr);
}

uint32_t system_ticks( void )
{
    TickType_t ticks = xTaskGetTickCount() * portTICK_PERIOD_MS;
    return ( uint32_t ) ticks;
}

uint32_t system_timestamp()
{
    time_t now;
    // struct tm timeinfo;
    time(&now);
    // localtime_r(&now, &timeinfo);
    return (uint32_t)now;
}

uint32_t system_random(void)
{
    return (uint32_t)(0xffffffff & rand());
}

// 静态内存池初始化
void static_buffer_init(void)
{
    if (static_buffer_initialized) {
        return;
    }

    // 创建互斥锁
    buffer_mutex = xSemaphoreCreateMutex();
    if (buffer_mutex == NULL) {
        return;
    }

    // 初始化小缓冲区池
    for (int i = 0; i < STATIC_BUFFER_SMALL_COUNT; i++) {
        small_pool[i].buffer = small_buffers[i];
        small_pool[i].size = STATIC_BUFFER_SMALL_SIZE;
        small_pool[i].in_use = 0;
    }

    // 初始化中等缓冲区池
    for (int i = 0; i < STATIC_BUFFER_MEDIUM_COUNT; i++) {
        medium_pool[i].buffer = medium_buffers[i];
        medium_pool[i].size = STATIC_BUFFER_MEDIUM_SIZE;
        medium_pool[i].in_use = 0;
    }

    // 初始化大缓冲区池
    for (int i = 0; i < STATIC_BUFFER_LARGE_COUNT; i++) {
        large_pool[i].buffer = large_buffers[i];
        large_pool[i].size = STATIC_BUFFER_LARGE_SIZE;
        large_pool[i].in_use = 0;
    }

    // 初始化超大缓冲区池
    for (int i = 0; i < STATIC_BUFFER_XLARGE_COUNT; i++) {
        xlarge_pool[i].buffer = xlarge_buffers[i];
        xlarge_pool[i].size = STATIC_BUFFER_XLARGE_SIZE;
        xlarge_pool[i].in_use = 0;
    }

    static_buffer_initialized = true;
}

// 静态缓冲区分配
void* static_buffer_alloc(size_t size)
{
    if (!static_buffer_initialized) {
        static_buffer_init();
    }

    if (buffer_mutex == NULL) {
        return NULL;
    }

    void* result = NULL;

    if (xSemaphoreTake(buffer_mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        // 选择合适大小的缓冲区池
        if (size <= STATIC_BUFFER_SMALL_SIZE) {
            for (int i = 0; i < STATIC_BUFFER_SMALL_COUNT; i++) {
                if (!small_pool[i].in_use) {
                    small_pool[i].in_use = 1;
                    result = small_pool[i].buffer;
                    memset(result, 0, size);
                    break;
                }
            }
        } else if (size <= STATIC_BUFFER_MEDIUM_SIZE) {
            for (int i = 0; i < STATIC_BUFFER_MEDIUM_COUNT; i++) {
                if (!medium_pool[i].in_use) {
                    medium_pool[i].in_use = 1;
                    result = medium_pool[i].buffer;
                    memset(result, 0, size);
                    break;
                }
            }
        } else if (size <= STATIC_BUFFER_LARGE_SIZE) {
            for (int i = 0; i < STATIC_BUFFER_LARGE_COUNT; i++) {
                if (!large_pool[i].in_use) {
                    large_pool[i].in_use = 1;
                    result = large_pool[i].buffer;
                    memset(result, 0, size);
                    break;
                }
            }
        } else if (size <= STATIC_BUFFER_XLARGE_SIZE) {
            for (int i = 0; i < STATIC_BUFFER_XLARGE_COUNT; i++) {
                if (!xlarge_pool[i].in_use) {
                    xlarge_pool[i].in_use = 1;
                    result = xlarge_pool[i].buffer;
                    memset(result, 0, size);
                    break;
                }
            }
        }

        xSemaphoreGive(buffer_mutex);
    }

    // 如果静态缓冲区不够用，回退到动态分配
    if (result == NULL) {
        result = malloc(size);
        if (result != NULL) {
            memset(result, 0, size);
        }
    }

    return result;
}

// 静态缓冲区释放
void static_buffer_free(void* ptr)
{
    if (ptr == NULL || buffer_mutex == NULL) {
        return;
    }

    bool found = false;

    if (xSemaphoreTake(buffer_mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        // 在小缓冲区池中查找
        for (int i = 0; i < STATIC_BUFFER_SMALL_COUNT; i++) {
            if (small_pool[i].buffer == ptr && small_pool[i].in_use) {
                small_pool[i].in_use = 0;
                found = true;
                break;
            }
        }

        // 在中等缓冲区池中查找
        if (!found) {
            for (int i = 0; i < STATIC_BUFFER_MEDIUM_COUNT; i++) {
                if (medium_pool[i].buffer == ptr && medium_pool[i].in_use) {
                    medium_pool[i].in_use = 0;
                    found = true;
                    break;
                }
            }
        }

        // 在大缓冲区池中查找
        if (!found) {
            for (int i = 0; i < STATIC_BUFFER_LARGE_COUNT; i++) {
                if (large_pool[i].buffer == ptr && large_pool[i].in_use) {
                    large_pool[i].in_use = 0;
                    found = true;
                    break;
                }
            }
        }

        // 在超大缓冲区池中查找
        if (!found) {
            for (int i = 0; i < STATIC_BUFFER_XLARGE_COUNT; i++) {
                if (xlarge_pool[i].buffer == ptr && xlarge_pool[i].in_use) {
                    xlarge_pool[i].in_use = 0;
                    found = true;
                    break;
                }
            }
        }

        xSemaphoreGive(buffer_mutex);
    }

    // 如果不是静态缓冲区，使用标准free释放
    if (!found) {
        free(ptr);
    }
}

#ifdef __cplusplus
}
#endif
